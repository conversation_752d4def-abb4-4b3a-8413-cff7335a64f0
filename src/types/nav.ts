import type { LucideIcon } from 'lucide-react'

export type NavType = 'LINK' | 'DROPDOWN'

export interface INavBase {
  id: string
  label: string
  to?: string
  img?: {
    light: string
    dark: string
    alt: string
  }
  description?: string
  icon?: LucideIcon
  type: NavType
}

export interface INavLink extends INavBase {
  type: 'LINK'
  to: string
}

export interface INavDropdown extends INavBase {
  type: 'DROPDOWN'
  children: INavLink[]
}

export type NavList = INavLink | INavDropdown
