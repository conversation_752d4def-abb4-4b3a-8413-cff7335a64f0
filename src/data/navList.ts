import { Briefcase, Building2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Users } from 'lucide-react'
import type { NavList } from '@/types/nav'

export const NAV_LIST: NavList[] = [
  { id: 'home', label: 'home', to: '/', type: 'LINK' },
  {
    id: 'about',
    label: 'about',
    type: 'DROPDOWN',
    children: [
      {
        id: 'company',
        label: 'company',
        to: '/company',
        type: 'LINK',
        description: 'Find out more about Team 37, our mission, and our values.',
        icon: Building2,
      },
      {
        id: 'team',
        label: 'team',
        to: '/team',
        type: 'LINK',
        description:
          'Behind Team 37 success is our team of lean, dedicated, and experienced creatives.',
        icon: Users,
      },
      {
        id: 'brand-guideline',
        label: 'brand guideline',
        to: '/brand-guideline',
        type: 'LINK',
        description: 'Logo usage, colors, and design rules for Team 37.',
        icon: Palette,
      },
    ],
  },
  {
    id: 'services',
    label: 'services',
    type: 'DROPDOWN',
    children: [
      {
        id: '37-dev',
        label: 'IT Development',
        to: '/services/37-dev',
        img: {
          light: '/light-37.dev.svg',
          dark: '/dark-37.dev.svg',
          alt: '37.dev - IT Development logo',
        },
        type: 'LINK',
        description:
          'Build high-converting websites—landing pages, company profiles, and e-commerce tailored for growing businesses.',
      },
      {
        id: '37-des',
        label: 'Design Graphics',
        to: '/services/37-des',
        img: {
          light: '/light-37.des.svg',
          dark: '/dark-37.des.svg',
          alt: '37.des - Graphic Design logo',
        },
        type: 'LINK',
        description:
          'Craft standout visuals—from logo design to full brand kits, packaging, and marketing assets.',
      },
      {
        id: '37-cre',
        label: 'Creative',
        to: '/services/37-cre',
        img: { light: '/light-37.cre.svg', dark: '/dark-37.cre.svg', alt: '37.cre - Creati logo' },
        type: 'LINK',
        description:
          'Drive engagement with scroll-stopping content, digital campaigns, and social media strategy that clicks.',
      },
    ],
  },
  { id: 'work', label: 'work', to: '/work', type: 'LINK' },
  { id: 'blog', label: 'blog', to: '/blog', type: 'LINK' },
  { id: 'contact', label: 'contact', to: '/contact', type: 'LINK' },
  {
    id: 'legal',
    label: 'legal',
    type: 'DROPDOWN',
    children: [
      {
        id: 'company-info',
        label: 'company info',
        to: '/legal/company-info',
        type: 'LINK',
        description: 'Legal entity, licenses, and brand ownership.',
        icon: Briefcase,
      },
      {
        id: 'payment-agreement',
        label: 'payment agreement',
        to: '/legal/payment-agreement',
        type: 'LINK',
        description: 'Payment terms, scope, and client rights.',
        icon: CreditCard,
      },
      {
        id: 'privacy-policy',
        label: 'privacy & policy',
        to: '/legal/privacy-policy',
        type: 'LINK',
        description: 'How we collect, store, and use data.',
        icon: ShieldCheck,
      },
    ],
  },
]
