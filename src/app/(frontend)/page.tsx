"use client";

import { InquiryForm } from "@/components/inquiry-form";
import Footer from "@/components/footer";
import Header from "@/components/header";
import { Typography } from "@/components/typography";
import { Button } from "@/components/ui/button";
import LightRays from "@/components/react-bits/backgrounds/LightRays/LightRays";
import { useTheme } from "next-themes";

const HomePage = () => {
	const { theme } = useTheme();

	return (
		<>
			<div className='fixed inset-0 w-full h-full pointer-events-none z-0'>
				<LightRays
					raysOrigin='top-right'
					raysColor={theme === "light" ? "#000" : "#ADFF00"}
					raysSpeed={0.8}
					lightSpread={1}
					rayLength={0.5}
					fadeDistance={1}
					saturation={1}
					followMouse={true}
					mouseInfluence={0.5}
					noiseAmount={0.3}
					distortion={0.2}
				/>
			</div>
			<div className='flex flex-col justify-between min-h-screen w-full relative z-10'>
				<Header />
				<div className='flex flex-col justify-between items-center align-middle'>
					<div className='flex flex-col justify-center items-center align-middle'>
						<div className='flex flex-col justify-center items-center align-middle w-auto sm:w-xl lg:w-3xl gap-8'>
							<Typography as='h1' size='h1' className='w-full text-center'>
								Build. Brand. Scale. All with One Partner.
							</Typography>
							<div className='flex flex-row justify-center align-middle items-center gap-4'>
								<Button size='lg' variant='outline'>
									Explore Our Services
								</Button>
								<InquiryForm btnText="Let's Build 🚀" />
							</div>
							<div className='flex flex-col justify-center items-center align-middle text-center'>
								<Typography as='p' size='p' variant='disabled'>
									Team 37 helps SMEs turn ideas into impact—through design,
									tech, and creative digital strategy.
								</Typography>
							</div>
						</div>
					</div>
				</div>
				<Footer />
			</div>
		</>
	);
};

export default HomePage;
