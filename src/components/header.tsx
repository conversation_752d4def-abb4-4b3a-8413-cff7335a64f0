import { InquiryForm } from './inquiry-form'
import { Logo } from './logo'
import { SelectVersion } from './select-version'
import { ThemeSwitcherToggle } from './theme-switcher'
import { Separator } from './ui/separator'
import { DesktopMenu, MobileNavigation } from './navmenu'

const Header = () => {
  return (
    <div>
      <div className="flex flex-row w-full justify-between my-4 z-50 align-middle items-center">
        {/* LEFT SIDE */}
        <div className="flex flex-row gap-4 items-center align-middle">
          <Logo
            href="#"
            width={128}
            height={128}
            lightLogoSrc="/light-team37-logo.svg"
            darkLogoSrc="/dark-team37-logo.svg"
            alt="Team 37 logo"
          />
          <SelectVersion />
        </div>

        {/* MIDDLE */}
        <div className="hidden md:block">
          <DesktopMenu />
        </div>
        <div className="block md:hidden">
          <MobileNavigation />
        </div>

        {/* RIGHT SIDE */}
        <div className="hidden md:flex flex-row gap-4 items-center align-middle">
          <ThemeSwitcherToggle />
          <InquiryForm variant="default" btnText="Let's Build 🚀" />
        </div>
      </div>
      <Separator />
    </div>
  )
}

export default Header
