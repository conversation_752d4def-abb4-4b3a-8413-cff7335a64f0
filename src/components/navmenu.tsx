'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
} from '@/components/ui/navigation-menu'
import { Menu } from 'lucide-react'
import { NAV_LIST } from '@/data/navList'
import { cn } from '@/lib/utils'
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>etHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import { Typography } from '@/components/typography'
import { ScrollArea } from './ui/scroll-area'
import { ThemeSwitcherToggle } from './theme-switcher'
import { InquiryForm } from './inquiry-form'

export const DesktopMenu = () => {
  const { resolvedTheme } = useTheme()

  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        {NAV_LIST.map((item) => (
          <NavigationMenuItem key={item.id}>
            {item.type === 'DROPDOWN' ? (
              <>
                <NavigationMenuTrigger className="capitalize bg-transparent">
                  {item.label}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-2 p-4 w-72 sm:w-96">
                    {item.children?.map((child) => (
                      <li key={child.id}>
                        <NavigationMenuLink asChild>
                          {/* TODO: Add icon here */}
                          <Link
                            href={child.to}
                            className="flex items-start rounded-md hover:bg-accent transition"
                          >
                            <div className="flex flex-row gap-2 items-center w-full align-middle">
                              {child.icon && (
                                <child.icon className="w-4 h-4 text-muted-foreground" />
                              )}
                              {child.img && (
                                <Image
                                  src={resolvedTheme === 'dark' ? child.img.dark : child.img.light}
                                  alt={child.img.alt}
                                  width={64}
                                  height={64}
                                />
                              )}
                              <Typography as="p" className="capitalize text-sm font-medium">
                                {child.label}
                              </Typography>
                            </div>
                            <Typography
                              as="p"
                              className="text-sm w-full font-normal text-light-gray-11 dark:text-dark-gray-11"
                            >
                              {child.description}
                            </Typography>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </>
            ) : (
              <NavigationMenuLink asChild>
                <Link
                  href={item.to}
                  className={cn(
                    'px-4 py-2 text-md font-medium capitalize hover:text-primary transition-colors',
                  )}
                >
                  {item.label}
                </Link>
              </NavigationMenuLink>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  )
}

export const MobileNavigation = () => {
  const { resolvedTheme } = useTheme()

  return (
    <Sheet>
      <SheetTrigger className="md:hidden">
        <Menu className="h-6 w-6" />
      </SheetTrigger>
      <SheetContent side="right" className="w-full max-w-80 sm:max-w-96">
        <SheetHeader>
          <SheetTitle className="text-xl font-bold">Menu</SheetTitle>
        </SheetHeader>
        <ScrollArea className="relative h-128 w-full overflow-y-auto" type="always">
          {/* TOP SHADOW */}
          <div className="pointer-events-none absolute top-0 left-0 w-full h-6 bg-gradient-to-b from-[#f0f0f0]/90 to-transparent dark:from-[#0f0f0f]/80 dark:to-transparent z-10" />

          <div className="p-4 flex flex-col gap-4">
            {NAV_LIST.map((item) =>
              item.type === 'DROPDOWN' ? (
                <div key={item.id}>
                  <div className="text-sm font-semibold capitalize">{item.label}</div>
                  <ul className="ml-4 mt-2 space-y-4">
                    {item.children?.map((child) => (
                      <li key={child.id}>
                        <Link
                          href={child.to}
                          className="flex flex-col capitalize gap-2 items-start text-sm hover:underline"
                        >
                          <div className="flex flex-row gap-2">
                            {child.icon && <child.icon className="w-4 h-4 text-muted-foreground" />}
                            {child.img && (
                              <Image
                                src={resolvedTheme === 'dark' ? child.img.dark : child.img.light}
                                alt={child.label}
                                width={64}
                                height={64}
                              />
                            )}
                            {child.label}
                          </div>
                          {child.description && (
                            <Typography
                              as="p"
                              className="text-light-gray-11 text-xs tracking-wide dark:text-dark-gray-11"
                            >
                              {child.description}
                            </Typography>
                          )}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <Link
                  key={item.id}
                  href={item.to}
                  className="text-sm font-medium capitalize hover:underline"
                >
                  {item.label}
                </Link>
              ),
            )}
          </div>

          {/* BOTTOM SHADOW */}
          <div className="pointer-events-none absolute bottom-0 left-0 w-full h-6 bg-gradient-to-t from-[#f0f0f0]/90 to-transparent dark:from-[#0f0f0f]/80 dark:to-transparent z-10" />
        </ScrollArea>
        <SheetFooter className="flex flex-row gap-4 items-center align-middle mr-12">
          <ThemeSwitcherToggle />
          <InquiryForm className="w-full" variant="default" btnText="Let's Build 🚀" />
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
