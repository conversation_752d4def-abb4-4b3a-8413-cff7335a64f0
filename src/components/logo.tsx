'use client'

import { useMounted } from '@/hooks/useMounted'
import { cn } from '@/lib/utils'
import { useTheme } from 'next-themes'
import Image from 'next/image'
import Link from 'next/link'

type LogoProps = {
  href?: string
  lightLogoSrc: string
  darkLogoSrc: string
  width?: number
  height?: number
  alt?: string
  className?: string
}

export const Logo = ({
  href = '#',
  lightLogoSrc,
  darkLogoSrc,
  width = 128,
  height = 128,
  alt = 'This is alt text',
  className = '',
}: LogoProps) => {
  const mounted = useMounted()
  const { theme, systemTheme } = useTheme()

  if (!mounted) {
    return <div style={{ width, height }} />
  }

  const currTheme = theme === 'system' ? systemTheme : theme

  return (
    <Link
      href={href}
      className={cn(className, 'flex items-center justify-center')}
      rel="noreferrer noopener"
      target="_self"
    >
      <Image
        src={currTheme === 'light' ? lightLogoSrc : darkLogoSrc}
        width={width}
        height={height}
        alt={alt}
      />
    </Link>
  )
}
