import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { VERSION_WEB_LIST } from '@/data/versionWebList'
import { useRouter } from 'next/navigation'

export const SelectVersion = () => {
  const router = useRouter()

  const latestVersion = VERSION_WEB_LIST.at(-1)

  return (
    <Select
      defaultValue={latestVersion?.label}
      onValueChange={(label) => {
        const found = VERSION_WEB_LIST.find((v) => v.label === label)
        if (found) router.push(found.to)
      }}
    >
      <SelectTrigger>
        <SelectValue placeholder={latestVersion?.label} />
      </SelectTrigger>
      <SelectContent>
        {[...VERSION_WEB_LIST]
          .sort((a, b) => b.id.localeCompare(a.id))
          .map((version) => (
            <SelectItem key={version.id} value={version.label}>
              {version.label}
            </SelectItem>
          ))}
      </SelectContent>
    </Select>
  )
}
