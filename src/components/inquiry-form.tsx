'use client'

type InquiryFormProps = {
  className?: string
  btnText?: string
  variant?: 'default' | 'destructive' | 'ghost' | 'link' | 'outline' | 'secondary'
}

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './ui/form'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import Link from 'next/link'
import { Button } from './ui/button'
import { createClient } from '@/utils/supabase/client'
import { useState } from 'react'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { cn } from '@/lib/utils'

const allowedChars = /^[a-zA-Z0-9 .,'!?-]+$/
const phoneNumValidation = /^\+?[0-9]+$/

const formSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(255, { message: 'Max. 255 characters are allowed' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  company: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(255, { message: 'Min. 255 character' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  role: z
    .string()
    .min(3, { message: 'Min. 3 character' })
    .max(50, { message: 'Min. 50 character' })
    .regex(allowedChars, 'Only letters, numbers, spaces are allowed'),
  phone_number: z
    .string()
    .min(10, { message: 'Min. 10 characters' })
    .max(13, { message: 'Max. 13 characters' })
    .regex(phoneNumValidation, 'Only numbers are allowed'),
  work_email: z
    .string()
    .email()
    .min(3, { message: 'Min. 3 characters' })
    .max(255, { message: 'Max. 3 characters' }),
  services: z.string(),
  inquiry: z
    .string()
    .min(10, { message: 'Min. 10 characters' })
    .max(300, { message: 'Max. 300 characters' }),
})

export const InquiryForm = ({
  className,
  btnText = 'Get Started',
  variant = 'default',
}: InquiryFormProps) => {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const serviceList: { id: string; label: string }[] = [
    { id: '1', label: '37.dev - Web Development' },
    { id: '2', label: '37.des - Graphic Design' },
    { id: '3', label: '37.cre - Creative' },
  ]

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      phone_number: '',
      work_email: '',
      company: '',
      role: '',
      services: '',
      inquiry: '',
    },
  })

  const submitForm = async () => {
    setIsLoading(true)

    const supabase = createClient()
    const data = form.getValues()

    const { error } = await supabase.schema('public').from('inquiryform').insert([data])

    if (error) {
      toast.error('Submission Failed', {
        description: error.message || 'Something went wrong. Please try again.',
      })
      setIsLoading(false)
      return
    }

    toast.success('Submission Succesful', {
      description: "We've received your inquiry. We'll be in touch soon.",
    })
    form.reset()
    setTimeout(() => {
      setOpen(false)
      setIsLoading(false)
    }, 300)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className={cn(className)} size="lg" variant={variant}>
          {btnText}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Inquiry Form</DialogTitle>
          <DialogDescription>Please fill out the form below to get started.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            id="inquiry-form"
            onSubmit={form.handleSubmit(submitForm)}
            className="flex flex-col space-y-6"
          >
            <div>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Andrew V." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-row gap-4 w-full">
              <FormField
                control={form.control}
                name="phone_number"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="+62 812-8787-2257" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
                rules={{ required: true }}
              />
              <FormField
                control={form.control}
                name="work_email"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Work Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-row gap-4 w-full">
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Company</FormLabel>
                    <FormControl>
                      <Input placeholder="Team 37" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Role</FormLabel>
                    <FormControl>
                      <Input placeholder="Marketing" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="services"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Select Services</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select here..." />
                        </SelectTrigger>
                        <SelectContent>
                          {serviceList.map((service) => (
                            <SelectItem key={service.id} value={service.label}>
                              {service.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="inquiry"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="capitalize">Describe your needs</FormLabel>
                    <FormControl>
                      <Textarea maxLength={300} placeholder="I need help with..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
        <DialogDescription>
          By clicking submit, you&apos;re agree to our{' '}
          <Link className="underline text-light-gray-12 dark:text-dark-gray-12" href="tnc">
            terms and conditions.
          </Link>
        </DialogDescription>
        <DialogFooter>
          <Button type="submit" form="inquiry-form" disabled={isLoading}>
            {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Submit'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
