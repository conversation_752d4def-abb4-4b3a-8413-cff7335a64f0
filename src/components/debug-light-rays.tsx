'use client'

import { useRef, useEffect, useState } from 'react'

const DebugLightRays = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [debugInfo, setDebugInfo] = useState<string>('')

  useEffect(() => {
    if (!containerRef.current) return

    const testWebGL = () => {
      try {
        const canvas = document.createElement('canvas')
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
        
        if (!gl) {
          setDebugInfo('WebGL not supported')
          return
        }

        setDebugInfo(`WebGL supported: ${gl.getParameter(gl.VERSION)}`)
        
        // Test basic rendering
        canvas.width = 400
        canvas.height = 300
        canvas.style.width = '100%'
        canvas.style.height = '100%'
        canvas.style.border = '2px solid #ADFF00'
        
        // Clear with green color to test
        gl.clearColor(0.0, 1.0, 0.0, 0.3)
        gl.clear(gl.COLOR_BUFFER_BIT)
        
        if (containerRef.current) {
          containerRef.current.appendChild(canvas)
        }
        
      } catch (error) {
        setDebugInfo(`WebGL error: ${error}`)
      }
    }

    testWebGL()
  }, [])

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 text-white p-4 rounded">
      <div>Debug Info: {debugInfo}</div>
      <div 
        ref={containerRef} 
        className="w-32 h-24 mt-2 border border-white"
        style={{ minHeight: '96px' }}
      />
    </div>
  )
}

export default DebugLightRays
