import type { CollectionConfig } from 'payload'

export const InquiryForm: CollectionConfig = {
  slug: 'inquiryform',
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Name',
    },
    {
      name: 'company',
      type: 'text',
      required: true,
      label: 'Company',
    },
    {
      name: 'role',
      type: 'text',
      required: true,
      label: 'Role',
    },
    {
      name: 'phone-number',
      type: 'text',
      required: true,
      label: 'Phone Number',
    },
    {
      name: 'work-email',
      type: 'email',
      required: true,
      label: 'Work Email',
    },
    {
      name: 'inquiry',
      type: 'textarea',
      required: true,
      label: 'Inquiry',
    },
    {
      name: 'services',
      type: 'text',
      required: true,
      label: 'Services',
    },
  ],
}
