{"name": "team37-web", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbopack", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@payloadcms/db-postgres": "3.43.0", "@payloadcms/next": "3.43.0", "@payloadcms/payload-cloud": "3.43.0", "@payloadcms/plugin-form-builder": "^3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/ui": "3.43.0", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@tailwindcss/postcss": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "graphql": "^16.8.1", "gsap": "^3.13.0", "lucide-react": "^0.522.0", "next": "15.3.0", "next-themes": "^0.4.6", "ogl": "^1.0.11", "payload": "3.43.0", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.59.0", "sharp": "0.32.6", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "prettier": "^3.4.2", "tw-animate-css": "^1.3.4", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}